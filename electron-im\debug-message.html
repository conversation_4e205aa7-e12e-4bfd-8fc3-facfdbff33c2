<!DOCTYPE html>
<html>
<head>
    <title>消息调试页面</title>
    <style>
        body { font-family: monospace; padding: 20px; }
        .log { margin: 5px 0; padding: 5px; background: #f0f0f0; }
        .error { background: #ffebee; }
        .success { background: #e8f5e9; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>消息调试页面</h1>
    <p>当前环境: <span id="environment"></span></p>
    
    <h2>测试按钮</h2>
    <button onclick="testLocalStorage()">测试 localStorage</button>
    <button onclick="testWebSocket()">测试 WebSocket 连接</button>
    <button onclick="simulateMessage()">模拟接收消息</button>
    <button onclick="clearLogs()">清除日志</button>
    
    <h2>调试日志</h2>
    <div id="logs"></div>

    <script>
        // 检测环境
        const isElectron = typeof window !== 'undefined' && window.process && window.process.type === 'renderer';
        document.getElementById('environment').textContent = isElectron ? 'Electron' : 'Web';
        
        function log(message, type = 'info') {
            const logs = document.getElementById('logs');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logs.insertBefore(div, logs.firstChild);
        }
        
        function testLocalStorage() {
            try {
                const testKey = 'test_key';
                const testValue = { test: 'value', timestamp: Date.now() };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                if (retrieved && retrieved.test === 'value') {
                    log('localStorage 测试成功', 'success');
                } else {
                    log('localStorage 测试失败：数据不匹配', 'error');
                }
                
                localStorage.removeItem(testKey);
            } catch (error) {
                log(`localStorage 测试失败：${error.message}`, 'error');
            }
        }
        
        function testWebSocket() {
            try {
                const wsUrl = isElectron ? 'ws://***********:3000/ws' : `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/zhuyuqian/ws`;
                log(`尝试连接 WebSocket: ${wsUrl}`);
                
                const ws = new WebSocket(wsUrl + '?token=test');
                
                ws.onopen = () => {
                    log('WebSocket 连接成功', 'success');
                    ws.close();
                };
                
                ws.onerror = (error) => {
                    log(`WebSocket 连接失败: ${error}`, 'error');
                };
                
                ws.onclose = (event) => {
                    log(`WebSocket 连接关闭: ${event.code} ${event.reason}`);
                };
                
            } catch (error) {
                log(`WebSocket 测试失败：${error.message}`, 'error');
            }
        }
        
        function simulateMessage() {
            log('模拟接收消息...');
            
            // 模拟一个消息对象
            const mockMessage = {
                id: `test_${Date.now()}`,
                senderId: 'test_sender',
                receiverId: 'current_user',
                content: '这是一条测试消息',
                timestamp: Date.now()
            };
            
            log(`模拟消息: ${JSON.stringify(mockMessage)}`);
            
            // 检查是否有 Vue 应用实例
            if (window.Vue || window.__VUE__) {
                log('检测到 Vue 应用', 'success');
            } else {
                log('未检测到 Vue 应用', 'error');
            }
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        // 初始化日志
        log(`页面加载完成，环境：${isElectron ? 'Electron' : 'Web'}`);
    </script>
</body>
</html>
